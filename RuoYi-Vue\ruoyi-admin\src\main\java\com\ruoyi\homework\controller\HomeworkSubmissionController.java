package com.ruoyi.homework.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.homework.service.IHomeworkSubmissionService;
import com.ruoyi.common.utils.SecurityUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HomeworkSubmission;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 作业提交Controller
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
@RestController
@RequestMapping("/system/submission")
public class HomeworkSubmissionController extends BaseController
{

    /*@Autowired
    IHomeworkSubmissionService homeworkSubmissionService;*/

    @DubboReference(group = "HomeworkSubmission",version = "1.0.0")
    private IHomeworkSubmissionService homeworkSubmissionService;

    /**
     * 查询作业提交列表
     */
    @PreAuthorize("@ss.hasPermi('system:submission:list')")
    @GetMapping("/list")
    public TableDataInfo list(HomeworkSubmission homeworkSubmission)
    {
        startPage();
        List<HomeworkSubmission> list = homeworkSubmissionService.selectHomeworkSubmissionList(homeworkSubmission);
        return getDataTable(list);
    }

    /**
     * 导出作业提交列表
     */
    @PreAuthorize("@ss.hasPermi('system:submission:export')")
    @Log(title = "作业提交", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeworkSubmission homeworkSubmission)
    {
        List<HomeworkSubmission> list = homeworkSubmissionService.selectHomeworkSubmissionList(homeworkSubmission);
        ExcelUtil<HomeworkSubmission> util = new ExcelUtil<HomeworkSubmission>(HomeworkSubmission.class);
        util.exportExcel(response, list, "作业提交数据");
    }

    /**
     * 获取作业提交详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:submission:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkSubmissionService.selectHomeworkSubmissionById(id));
    }

    /**
     * 新增作业提交
     */
    @PreAuthorize("@ss.hasPermi('system:submission:add')")
    @Log(title = "作业提交", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeworkSubmission homeworkSubmission)
    {
        Long userId = SecurityUtils.getUserId();
        String nickName = SecurityUtils.getNickName();
        homeworkSubmission.setStudentId(userId);
        homeworkSubmission.setStudentName(nickName);
        return toAjax(homeworkSubmissionService.insertHomeworkSubmission(homeworkSubmission));
    }

    /**
     * 修改作业提交
     */
    @PreAuthorize("@ss.hasPermi('system:submission:edit')")
    @Log(title = "作业提交", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeworkSubmission homeworkSubmission)
    {
        return toAjax(homeworkSubmissionService.updateHomeworkSubmission(homeworkSubmission));
    }

    /**
     * 删除作业提交
     */
    @PreAuthorize("@ss.hasPermi('system:submission:remove')")
    @Log(title = "作业提交", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkSubmissionService.deleteHomeworkSubmissionByIds(ids));
    }
}
