package com.ruoyi.system.domain;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 作业任务对象 homework_task
 *
 * <AUTHOR>
 * @date 2025-06-18
 */
public class HomeworkTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 作业标题
     */
    @Excel(name = "作业标题")
    private String title;

    /**
     * 作业内容
     */
    @Excel(name = "作业内容")
    private String content;

    /**
     * 作业附件
     */
    @Excel(name = "作业附件")
    private String taskFile;

    /**
     * 发布时
     * 间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date publishTime;

    /**
     * 截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "截止时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date deadline;

    /**
     * 发布人（教师ID，关联sys_user）
     */
    @Excel(name = "发布人", readConverterExp = "教=师ID，关联sys_user")
    private Long teacherId;

    /**
     * 状态：0未截止 1已截止
     */
    @Excel(name = "状态：0未截止 1已截止")
    private Integer status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    /**
     * 教师姓名
     */
    @Excel(name = "教师姓名")
    private String teacherName;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setTaskFile(String taskFile) {
        this.taskFile = taskFile;
    }

    public String getTaskFile() {
        return taskFile;
    }

    public void setPublishTime(Date publishTime) {
        this.publishTime = publishTime;
    }

    public Date getPublishTime() {
        return publishTime;
    }

    public void setDeadline(Date deadline) {
        this.deadline = deadline;
    }

    public Date getDeadline() {
        return deadline;
    }

    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() {
        return teacherId;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getTeacherName() {
        return teacherName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("content", getContent())
                .append("taskFile", getTaskFile())
                .append("publishTime", getPublishTime())
                .append("deadline", getDeadline())
                .append("teacherId", getTeacherId())
                .append("status", getStatus())
                .append("delFlag", getDelFlag())
                .append("teacherName", getTeacherName())
                .toString();
    }
}
