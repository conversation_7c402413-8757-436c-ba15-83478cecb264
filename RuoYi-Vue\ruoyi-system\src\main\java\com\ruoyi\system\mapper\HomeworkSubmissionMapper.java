package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.HomeworkSubmission;

/**
 * 作业提交Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */
public interface HomeworkSubmissionMapper 
{
    /**
     * 查询作业提交
     * 
     * @param id 作业提交主键
     * @return 作业提交
     */
    public HomeworkSubmission selectHomeworkSubmissionById(Long id);

    /**
     * 查询作业提交列表
     * 
     * @param homeworkSubmission 作业提交
     * @return 作业提交集合
     */
    public List<HomeworkSubmission> selectHomeworkSubmissionList(HomeworkSubmission homeworkSubmission);

    /**
     * 新增作业提交
     * 
     * @param homeworkSubmission 作业提交
     * @return 结果
     */
    public int insertHomeworkSubmission(HomeworkSubmission homeworkSubmission);

    /**
     * 修改作业提交
     * 
     * @param homeworkSubmission 作业提交
     * @return 结果
     */
    public int updateHomeworkSubmission(HomeworkSubmission homeworkSubmission);

    /**
     * 删除作业提交
     * 
     * @param id 作业提交主键
     * @return 结果
     */
    public int deleteHomeworkSubmissionById(Long id);

    /**
     * 批量删除作业提交
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteHomeworkSubmissionByIds(Long[] ids);
}
