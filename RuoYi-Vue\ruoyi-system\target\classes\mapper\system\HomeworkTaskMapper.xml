<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HomeworkTaskMapper">

    <resultMap type="HomeworkTask" id="HomeworkTaskResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="taskFile"    column="task_file"    />
        <result property="publishTime"    column="publish_time"    />
        <result property="deadline"    column="deadline"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="teacherName"    column="teacher_name"    />
    </resultMap>

    <sql id="selectHomeworkTaskVo">
        select id, title, content, task_file, publish_time, deadline, teacher_id, status, del_flag, teacher_name from homework_task
    </sql>

    <select id="selectHomeworkTaskList" parameterType="HomeworkTask" resultMap="HomeworkTaskResult">
        <include refid="selectHomeworkTaskVo"/>
        <where>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="taskFile != null  and taskFile != ''"> and task_file = #{taskFile}</if>
            <if test="publishTime != null "> and publish_time = #{publishTime}</if>
            <if test="deadline != null "> and deadline = #{deadline}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="status != null "> and status = #{status}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
        </where>
    </select>

    <select id="selectHomeworkTaskById" parameterType="Long" resultMap="HomeworkTaskResult">
        <include refid="selectHomeworkTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeworkTask" parameterType="HomeworkTask" useGeneratedKeys="true" keyProperty="id">
        insert into homework_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">title,</if>
            <if test="content != null">content,</if>
            <if test="taskFile != null">task_file,</if>
            <if test="publishTime != null">publish_time,</if>
            <if test="deadline != null">deadline,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="teacherName != null">teacher_name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null">#{content},</if>
            <if test="taskFile != null">#{taskFile},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="deadline != null">#{deadline},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="teacherName != null">#{teacherName},</if>
        </trim>
    </insert>

    <update id="updateHomeworkTask" parameterType="HomeworkTask">
        update homework_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null">content = #{content},</if>
            <if test="taskFile != null">task_file = #{taskFile},</if>
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="deadline != null">deadline = #{deadline},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="teacherName != null">teacher_name = #{teacherName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeworkTaskById" parameterType="Long">
        delete from homework_task where id = #{id}
    </delete>

    <delete id="deleteHomeworkTaskByIds" parameterType="String">
        delete from homework_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>