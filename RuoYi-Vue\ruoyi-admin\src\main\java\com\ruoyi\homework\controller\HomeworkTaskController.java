package com.ruoyi.homework.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.homework.service.IHomeworkTaskService;
import com.ruoyi.common.utils.SecurityUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HomeworkTask;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 作业任务Controller
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@RestController
@RequestMapping("/system/task")
public class HomeworkTaskController extends BaseController
{

    @DubboReference(group = "HomeworkTask",version = "1.0.0")
    private IHomeworkTaskService homeworkTaskService;


    /**
     * 查询作业任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:task:list')")
    @GetMapping("/list")
    public TableDataInfo list(HomeworkTask homeworkTask)
    {
        startPage();
        List<HomeworkTask> list = homeworkTaskService.selectHomeworkTaskList(homeworkTask);
        return getDataTable(list);
    }

    /**
     * 导出作业任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:task:export')")
    @Log(title = "作业任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HomeworkTask homeworkTask)
    {
        List<HomeworkTask> list = homeworkTaskService.selectHomeworkTaskList(homeworkTask);
        ExcelUtil<HomeworkTask> util = new ExcelUtil<HomeworkTask>(HomeworkTask.class);
        util.exportExcel(response, list, "作业任务数据");
    }

    /**
     * 获取作业任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:task:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(homeworkTaskService.selectHomeworkTaskById(id));
    }

    /**
     * 新增作业任务
     */
    @PreAuthorize("@ss.hasPermi('system:task:add')")
    @Log(title = "作业任务", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HomeworkTask homeworkTask)
    {
        /*获取当前登录用户的user_id*/
        Long userId = SecurityUtils.getUserId();
        String nickName = SecurityUtils.getNickName();
        homeworkTask.setTeacherId(userId);
        homeworkTask.setTeacherName(nickName);
        try {
            homeworkTaskService.insertHomeworkTask(homeworkTask);
        }catch (Exception e){
            return AjaxResult.error("该任务已存在");
        }
        return toAjax(1);
    }

    /**
     * 修改作业任务
     */
    @PreAuthorize("@ss.hasPermi('system:task:edit')")
    @Log(title = "作业任务", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HomeworkTask homeworkTask)
    {
        return toAjax(homeworkTaskService.updateHomeworkTask(homeworkTask));
    }

    /**
     * 删除作业任务
     */
    @PreAuthorize("@ss.hasPermi('system:task:remove')")
    @Log(title = "作业任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(homeworkTaskService.deleteHomeworkTaskByIds(ids));
    }
}
