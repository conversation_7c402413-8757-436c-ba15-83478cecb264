package com.homework.service;

import com.ruoyi.system.domain.HomeworkSubmission;
import com.ruoyi.system.mapper.HomeworkSubmissionMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 作业提交Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-19
 */

@Service
@DubboService(group = "HomeworkSubmission",version = "1.0.0")
public class HomeworkSubmissionServiceImpl implements IHomeworkSubmissionService
{
    @Autowired
    private HomeworkSubmissionMapper homeworkSubmissionMapper;

    /**
     * 查询作业提交
     * 
     * @param id 作业提交主键
     * @return 作业提交
     */
    @Override
    public HomeworkSubmission selectHomeworkSubmissionById(Long id)
    {
        return homeworkSubmissionMapper.selectHomeworkSubmissionById(id);
    }

    /**
     * 查询作业提交列表
     * 
     * @param homeworkSubmission 作业提交
     * @return 作业提交
     */
    @Override
    public List<HomeworkSubmission> selectHomeworkSubmissionList(HomeworkSubmission homeworkSubmission)
    {
        /*打印本服务端口号*/
        System.out.println("服务provider被调用了");
        return homeworkSubmissionMapper.selectHomeworkSubmissionList(homeworkSubmission);
    }

    /**
     * 新增作业提交
     * 
     * @param homeworkSubmission 作业提交
     * @return 结果
     */
    @Override
    public int insertHomeworkSubmission(HomeworkSubmission homeworkSubmission)
    {
        return homeworkSubmissionMapper.insertHomeworkSubmission(homeworkSubmission);
    }

    /**
     * 修改作业提交
     * 
     * @param homeworkSubmission 作业提交
     * @return 结果
     */
    @Override
    public int updateHomeworkSubmission(HomeworkSubmission homeworkSubmission)
    {
        return homeworkSubmissionMapper.updateHomeworkSubmission(homeworkSubmission);
    }

    /**
     * 批量删除作业提交
     * 
     * @param ids 需要删除的作业提交主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkSubmissionByIds(Long[] ids)
    {
        return homeworkSubmissionMapper.deleteHomeworkSubmissionByIds(ids);
    }

    /**
     * 删除作业提交信息
     * 
     * @param id 作业提交主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkSubmissionById(Long id)
    {
        return homeworkSubmissionMapper.deleteHomeworkSubmissionById(id);
    }
}
