package com.ruoyi.homework.controller;

import com.homework.service.ITestService;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
@RequestMapping("/consumerTest")
public class ConsumerTestController {

    /*    @DubboReference(group = "test", version = "1.0.0")
        private ITestService testService;

        @RequestMapping("/consumer")
        public String test(String name) {
            return testService.test(name);
        }
*/
}
