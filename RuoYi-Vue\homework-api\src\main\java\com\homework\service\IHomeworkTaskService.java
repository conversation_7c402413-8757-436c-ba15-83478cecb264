package com.homework.service;

import com.ruoyi.system.domain.HomeworkTask;

import java.util.List;

/**
 * 作业任务Service接口
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
public interface IHomeworkTaskService 
{



    /**
     * 查询作业任务
     * 
     * @param id 作业任务主键
     * @return 作业任务
     */
    public HomeworkTask selectHomeworkTaskById(Long id);

    /**
     * 查询作业任务列表
     * 
     * @param homeworkTask 作业任务
     * @return 作业任务集合
     */
    public List<HomeworkTask> selectHomeworkTaskList(HomeworkTask homeworkTask);

    /**
     * 新增作业任务
     * 
     * @param homeworkTask 作业任务
     * @return 结果
     */
    public int insertHomeworkTask(HomeworkTask homeworkTask);

    /**
     * 修改作业任务
     * 
     * @param homeworkTask 作业任务
     * @return 结果
     */
    public int updateHomeworkTask(HomeworkTask homeworkTask);

    /**
     * 批量删除作业任务
     * 
     * @param ids 需要删除的作业任务主键集合
     * @return 结果
     */
    public int deleteHomeworkTaskByIds(Long[] ids);

    /**
     * 删除作业任务信息
     * 
     * @param id 作业任务主键
     * @return 结果
     */
    public int deleteHomeworkTaskById(Long id);
}
