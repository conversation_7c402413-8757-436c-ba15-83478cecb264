package com.homework.service;

import com.ruoyi.system.domain.HomeworkTask;
import com.ruoyi.system.mapper.HomeworkTaskMapper;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 作业任务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-18
 */
@Service
@DubboService(group = "HomeworkTask",version = "1.0.0")
public class HomeworkTaskServiceImpl implements IHomeworkTaskService 
{
    @Autowired
    private HomeworkTaskMapper homeworkTaskMapper;


    /**
     * 查询作业任务
     * 
     * @param id 作业任务主键
     * @return 作业任务
     */
    @Override
    public HomeworkTask selectHomeworkTaskById(Long id)
    {
        return homeworkTaskMapper.selectHomeworkTaskById(id);
    }

    /**
     * 查询作业任务列表
     * 
     * @param homeworkTask 作业任务
     * @return 作业任务
     */
    @Override
    public List<HomeworkTask> selectHomeworkTaskList(HomeworkTask homeworkTask)
    {
        System.out.println("服务provider被调用了");
        return homeworkTaskMapper.selectHomeworkTaskList(homeworkTask);
    }

    /**
     * 新增作业任务
     * 
     * @param homeworkTask 作业任务
     * @return 结果
     */
    @Override
    public int insertHomeworkTask(HomeworkTask homeworkTask)
    {
        return homeworkTaskMapper.insertHomeworkTask(homeworkTask);
    }

    /**
     * 修改作业任务
     * 
     * @param homeworkTask 作业任务
     * @return 结果
     */
    @Override
    public int updateHomeworkTask(HomeworkTask homeworkTask)
    {
        return homeworkTaskMapper.updateHomeworkTask(homeworkTask);
    }

    /**
     * 批量删除作业任务
     * 
     * @param ids 需要删除的作业任务主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkTaskByIds(Long[] ids)
    {
        return homeworkTaskMapper.deleteHomeworkTaskByIds(ids);
    }

    /**
     * 删除作业任务信息
     * 
     * @param id 作业任务主键
     * @return 结果
     */
    @Override
    public int deleteHomeworkTaskById(Long id)
    {
        return homeworkTaskMapper.deleteHomeworkTaskById(id);
    }
}
