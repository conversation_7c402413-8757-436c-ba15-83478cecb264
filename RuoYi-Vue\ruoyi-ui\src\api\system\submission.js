import request from '@/utils/request'

// 查询作业提交列表
export function listSubmission(query) {
  return request({
    url: '/system/submission/list',
    method: 'get',
    params: query
  })
}

// 查询作业提交详细
export function getSubmission(id) {
  return request({
    url: '/system/submission/' + id,
    method: 'get'
  })
}

// 新增作业提交
export function addSubmission(data) {
  return request({
    url: '/system/submission',
    method: 'post',
    data: data
  })
}

// 修改作业提交
export function updateSubmission(data) {
  return request({
    url: '/system/submission',
    method: 'put',
    data: data
  })
}

// 删除作业提交
export function delSubmission(id) {
  return request({
    url: '/system/submission/' + id,
    method: 'delete'
  })
}
