package com.ruoyi.system.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 作业提交对象 homework_submission
 *
 * <AUTHOR>
 * @date 2025-06-19
 */
public class HomeworkSubmission extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 作业ID（homework_task.id） */
    @Excel(name = "作业ID", readConverterExp = "h=omework_task.id")
    private Long taskId;

    /** 作业标题 */
    @Excel(name = "作业标题")
    private String taskTitle;

    /** 学生ID（sys_user.id） */
    @Excel(name = "学生ID", readConverterExp = "s=ys_user.id")
    private Long studentId;

    /** 提交内容 */
    @Excel(name = "提交内容")
    private String content;

    /** 提交时
     间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "提交时间", width = 30, dateFormat = "yyyy-MM-dd")
            private Date submitTime;

            /** 作业附件 */
            @Excel(name = "作业附件")
            private String taskSubFile;

            /** 学生姓名(sys_user.name) */
            @Excel(name = "学生姓名(sys_user.name)")
            private String studentName;

            /** 删除标志（0代表存在 2代表删除） */
            private String delFlag;

            /** 得分 */
            @Excel(name = "得分")
            private String score;

            /** 评语 */
            @Excel(name = "评语")
            private String comment;

            public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }

    public void setTaskId(Long taskId)
    {
        this.taskId = taskId;
    }

    public Long getTaskId()
    {
        return taskId;
    }

    public void setTaskTitle(String taskTitle)
    {
        this.taskTitle = taskTitle;
    }

    public String getTaskTitle()
    {
        return taskTitle;
    }

    public void setStudentId(Long studentId)
    {
        this.studentId = studentId;
    }

    public Long getStudentId()
    {
        return studentId;
    }

    public void setContent(String content)
    {
        this.content = content;
    }

    public String getContent()
    {
        return content;
    }

    public void setSubmitTime(Date submitTime)
    {
        this.submitTime = submitTime;
    }

    public Date getSubmitTime()
    {
        return submitTime;
    }

    public void setTaskSubFile(String taskSubFile)
    {
        this.taskSubFile = taskSubFile;
    }

    public String getTaskSubFile()
    {
        return taskSubFile;
    }

    public void setStudentName(String studentName)
    {
        this.studentName = studentName;
    }

    public String getStudentName()
    {
        return studentName;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setScore(String score)
    {
        this.score = score;
    }

    public String getScore()
    {
        return score;
    }

    public void setComment(String comment)
    {
        this.comment = comment;
    }

    public String getComment()
    {
        return comment;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("taskId", getTaskId())
                .append("taskTitle", getTaskTitle())
                .append("studentId", getStudentId())
                .append("content", getContent())
                .append("submitTime", getSubmitTime())
                .append("taskSubFile", getTaskSubFile())
                .append("studentName", getStudentName())
                .append("delFlag", getDelFlag())
                .append("score", getScore())
                .append("comment", getComment())
                .toString();
    }
}
