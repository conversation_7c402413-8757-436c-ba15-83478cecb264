<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.HomeworkSubmissionMapper">

    <resultMap type="HomeworkSubmission" id="HomeworkSubmissionResult">
        <result property="id"    column="id"    />
        <result property="taskId"    column="task_id"    />
        <result property="taskTitle"    column="task_title"    />
        <result property="studentId"    column="student_id"    />
        <result property="content"    column="content"    />
        <result property="submitTime"    column="submit_time"    />
        <result property="taskSubFile"    column="task_sub_file"    />
        <result property="studentName"    column="student_name"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="score"    column="score"    />
        <result property="comment"    column="comment"    />
    </resultMap>

    <sql id="selectHomeworkSubmissionVo">
        select id, task_id, task_title, student_id, content, submit_time, task_sub_file, student_name, del_flag, score, comment from homework_submission
    </sql>

    <select id="selectHomeworkSubmissionList" parameterType="HomeworkSubmission" resultMap="HomeworkSubmissionResult">
        <include refid="selectHomeworkSubmissionVo"/>
        <where>
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskTitle != null  and taskTitle != ''"> and task_title = #{taskTitle}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="submitTime != null "> and submit_time = #{submitTime}</if>
            <if test="taskSubFile != null  and taskSubFile != ''"> and task_sub_file = #{taskSubFile}</if>
            <if test="studentName != null  and studentName != ''"> and student_name like concat('%', #{studentName}, '%')</if>
            <if test="score != null  and score != ''"> and score = #{score}</if>
            <if test="comment != null  and comment != ''"> and comment = #{comment}</if>
        </where>
    </select>

    <select id="selectHomeworkSubmissionById" parameterType="Long" resultMap="HomeworkSubmissionResult">
        <include refid="selectHomeworkSubmissionVo"/>
        where id = #{id}
    </select>

    <insert id="insertHomeworkSubmission" parameterType="HomeworkSubmission" useGeneratedKeys="true" keyProperty="id">
        insert into homework_submission
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="taskTitle != null">task_title,</if>
            <if test="studentId != null">student_id,</if>
            <if test="content != null">content,</if>
            <if test="submitTime != null">submit_time,</if>
            <if test="taskSubFile != null">task_sub_file,</if>
            <if test="studentName != null">student_name,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="score != null">score,</if>
            <if test="comment != null">comment,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="taskTitle != null">#{taskTitle},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="content != null">#{content},</if>
            <if test="submitTime != null">#{submitTime},</if>
            <if test="taskSubFile != null">#{taskSubFile},</if>
            <if test="studentName != null">#{studentName},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="score != null">#{score},</if>
            <if test="comment != null">#{comment},</if>
        </trim>
    </insert>

    <update id="updateHomeworkSubmission" parameterType="HomeworkSubmission">
        update homework_submission
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskTitle != null">task_title = #{taskTitle},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="submitTime != null">submit_time = #{submitTime},</if>
            <if test="taskSubFile != null">task_sub_file = #{taskSubFile},</if>
            <if test="studentName != null">student_name = #{studentName},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="score != null">score = #{score},</if>
            <if test="comment != null">comment = #{comment},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHomeworkSubmissionById" parameterType="Long">
        delete from homework_submission where id = #{id}
    </delete>

    <delete id="deleteHomeworkSubmissionByIds" parameterType="String">
        delete from homework_submission where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>